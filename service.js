// service.js
import TrackPlayer, { Capability } from "react-native-track-player";
import { Event } from 'react-native-track-player';
import historyManager from './Utils/HistoryManager';

// Keep track of whether the player has been initialized
let isPlayerInitialized = false;

export const PlaybackService = async function() {
  try {
    // Only initialize player if not already initialized
    if (!isPlayerInitialized) {
      await TrackPlayer.setupPlayer();
      isPlayerInitialized = true;
      console.log('Player initialized successfully in service.js');
    } else {
      console.log('Player already initialized, skipping setup');
    }
    
    TrackPlayer.addEventListener(Event.RemotePlay, () => TrackPlayer.play());
    TrackPlayer.addEventListener(Event.RemotePause, () => TrackPlayer.pause());
    TrackPlayer.addEventListener(Event.RemoteNext, () => TrackPlayer.skipToNext());
    TrackPlayer.addEventListener(Event.RemotePrevious, () => TrackPlayer.skipToPrevious());
    TrackPlayer.addEventListener(Event.RemoteSeek, (e) => TrackPlayer.seekTo(e.position));

    // History tracking events
    TrackPlayer.addEventListener(Event.PlaybackActiveTrackChanged, async (event) => {
      try {
        console.log('Background: Track changed', event.track?.title);

        // Stop tracking previous song
        await historyManager.stopTracking();

        // Start tracking new song if it exists and player is playing
        if (event.track?.id) {
          const playerState = await TrackPlayer.getPlaybackState();
          if (playerState.state === 'playing') {
            await historyManager.startTracking(event.track);
          }
        }
      } catch (error) {
        console.error('Background: Error handling track change:', error);
      }
    });

    TrackPlayer.addEventListener(Event.PlaybackState, async (event) => {
      try {
        console.log('Background: Playback state changed', event.state);

        const currentTrack = await TrackPlayer.getActiveTrack();

        if (event.state === 'playing' && currentTrack && !historyManager.isCurrentlyTracking) {
          // Start tracking if playing and not already tracking
          await historyManager.startTracking(currentTrack);
        } else if (event.state === 'paused' || event.state === 'stopped') {
          // Save progress when paused/stopped but don't stop tracking completely
          await historyManager.saveProgressBackground();
        }
      } catch (error) {
        console.error('Background: Error handling playback state:', error);
      }
    });
    
    await TrackPlayer.updateOptions({
      // Media controls capabilities
      capabilities: [
        Capability.Play,
        Capability.Pause,
        Capability.SkipToNext,
        Capability.SkipToPrevious,
        Capability.SeekTo,
      ],
      notificationCapabilities: [
        Capability.Play,
        Capability.Pause,
        Capability.SkipToNext,
        Capability.SkipToPrevious,
        Capability.SeekTo,
      ],
      // Capabilities that will show up when the notification is in the compact form on Android
      compactCapabilities: [Capability.Play, Capability.Pause, Capability.SkipToNext,
        Capability.SkipToPrevious],
    });

    // Initialize history manager
    await historyManager.initialize();

    // Periodic save for background tracking
    setInterval(async () => {
      try {
        if (historyManager.isCurrentlyTracking) {
          await historyManager.saveProgressBackground();
        }
      } catch (error) {
        console.error('Background: Error in periodic save:', error);
      }
    }, 10000); // Save every 10 seconds in background

  } catch (error) {
    // Check if the error is about the player already being initialized
    if (error.message && error.message.includes('player has already been initialized')) {
      console.log('Player already initialized in service.js');
      isPlayerInitialized = true;
    } else {
      console.error('Error initializing player in service.js:', error);
    }
  }
};

