import AsyncStorage from '@react-native-async-storage/async-storage';
import { analyticsService } from './AnalyticsUtils';

// Storage keys
const HISTORY_STORAGE_KEY = 'orbit_listening_history';
const WEEKLY_STATS_KEY = 'orbit_weekly_stats';

// History entry structure
const createHistoryEntry = (song, listenDuration = 0) => ({
  id: song.id || Date.now().toString(),
  title: song.title || 'Unknown Title',
  artist: song.artist || 'Unknown Artist',
  artwork: song.artwork || song.image || '',
  url: song.url || '',
  duration: song.duration || 0,
  listenDuration: Math.max(0, listenDuration), // Ensure non-negative
  playCount: 1,
  lastPlayed: Date.now(),
  firstPlayed: Date.now(),
  sourceType: song.sourceType || (song.isLocal ? 'local' : song.path ? 'downloaded' : 'online'),
  isLocal: song.isLocal || false,
  path: song.path || null,
});

// Weekly stats structure
const createWeeklyStats = () => {
  const now = new Date();
  const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
  startOfWeek.setHours(0, 0, 0, 0);
  
  return {
    weekStart: startOfWeek.getTime(),
    totalListenTime: 0,
    songsPlayed: 0,
    dailyStats: Array(7).fill(0), // Sunday to Saturday
  };
};

class HistoryManager {
  constructor() {
    this.currentTrack = null;
    this.startTime = null;
    this.isTracking = false;
    this.trackingInterval = null;
    this.lastSavedDuration = 0;
    this.saveThreshold = 5000; // Save every 5 seconds
    this.minListenDuration = 10000; // 10 seconds minimum to count as "listened"
    this.hasCountedPlay = false; // Flag to ensure we only count play once per session
  }

  // Initialize history tracking
  async initialize() {
    try {
      console.log('HistoryManager: Initializing...');
      await this.ensureWeeklyStatsExist();
      console.log('HistoryManager: Initialized successfully');
    } catch (error) {
      console.error('HistoryManager: Initialization failed:', error);
    }
  }

  // Start tracking a song
  async startTracking(song) {
    try {
      if (!song || !song.id) {
        console.warn('HistoryManager: Invalid song provided for tracking');
        return;
      }

      // Check if we're already tracking this same song
      if (this.isTracking && this.currentTrack && this.currentTrack.id === song.id) {
        console.log(`HistoryManager: Already tracking "${song.title}"`);
        return;
      }

      // Stop previous tracking
      await this.stopTracking();

      this.currentTrack = song;
      this.startTime = Date.now();
      this.lastSavedDuration = 0;
      this.isTracking = true;
      this.hasCountedPlay = false; // Flag to ensure we only count play once

      console.log(`HistoryManager: Started tracking "${song.title}"`);

      // Start periodic saving
      this.trackingInterval = setInterval(() => {
        this.saveProgress();
      }, this.saveThreshold);

    } catch (error) {
      console.error('HistoryManager: Error starting tracking:', error);
    }
  }

  // Stop tracking current song
  async stopTracking() {
    try {
      if (!this.isTracking || !this.currentTrack) {
        return;
      }

      const listenDuration = Date.now() - this.startTime;

      // Save final progress
      await this.saveProgress(true);

      // Clear tracking state
      this.isTracking = false;
      this.currentTrack = null;
      this.startTime = null;
      this.lastSavedDuration = 0;
      this.hasCountedPlay = false; // Reset play count flag

      if (this.trackingInterval) {
        clearInterval(this.trackingInterval);
        this.trackingInterval = null;
      }

      console.log(`HistoryManager: Stopped tracking, duration: ${listenDuration}ms`);

    } catch (error) {
      console.error('HistoryManager: Error stopping tracking:', error);
      // Ensure cleanup even on error
      this.isTracking = false;
      this.currentTrack = null;
      this.startTime = null;
      this.lastSavedDuration = 0;
      this.hasCountedPlay = false;
      if (this.trackingInterval) {
        clearInterval(this.trackingInterval);
        this.trackingInterval = null;
      }
    }
  }

  // Save current progress
  async saveProgress(isFinal = false) {
    try {
      if (!this.isTracking || !this.currentTrack || !this.startTime) {
        return;
      }

      const currentDuration = Date.now() - this.startTime;

      // Only save if we've made significant progress or it's final
      if (!isFinal && currentDuration - this.lastSavedDuration < this.saveThreshold) {
        return;
      }

      // Check if we should count this as a new play (only once per tracking session)
      const shouldCountPlay = !this.hasCountedPlay && currentDuration >= this.minListenDuration;

      if (shouldCountPlay) {
        this.hasCountedPlay = true;
        await this.addToHistory(this.currentTrack, currentDuration, true);
        console.log(`HistoryManager: New play counted for "${this.currentTrack.title}"`);
      } else {
        // Just update duration without counting play
        await this.addToHistory(this.currentTrack, currentDuration, false);
      }

      this.lastSavedDuration = currentDuration;
      console.log(`HistoryManager: Progress saved - ${currentDuration}ms`);

    } catch (error) {
      console.error('HistoryManager: Error saving progress:', error);
    }
  }

  // Add or update song in history
  async addToHistory(song, listenDuration = 0, isNewPlay = false) {
    try {
      if (!song || !song.id) {
        console.warn('HistoryManager: Invalid song for history');
        return;
      }

      const history = await this.getHistory();
      const existingIndex = history.findIndex(item => item.id === song.id);

      if (existingIndex !== -1) {
        // Update existing entry
        const existing = history[existingIndex];

        // Only increment play count for new plays, not progress updates
        if (isNewPlay) {
          existing.playCount += 1;
        }

        // Update listen duration (keep the maximum)
        existing.listenDuration = Math.max(existing.listenDuration, listenDuration);
        existing.lastPlayed = Date.now();

        // Move to front of array (most recent first)
        history.splice(existingIndex, 1);
        history.unshift(existing);
      } else {
        // Create new entry
        const newEntry = createHistoryEntry(song, listenDuration);
        history.unshift(newEntry);
      }

      // Limit history size (keep last 1000 entries)
      if (history.length > 1000) {
        history.splice(1000);
      }

      await AsyncStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify(history));

      // Update weekly stats only for new plays
      if (isNewPlay) {
        await this.updateWeeklyStats(listenDuration);
      }

      // Track analytics only for significant listen duration
      if (isNewPlay && listenDuration >= this.minListenDuration) {
        analyticsService.logSongPlay(song.id, song.title, song.artist);
      }

    } catch (error) {
      console.error('HistoryManager: Error adding to history:', error);
    }
  }

  // Get full history
  async getHistory() {
    try {
      const historyData = await AsyncStorage.getItem(HISTORY_STORAGE_KEY);
      return historyData ? JSON.parse(historyData) : [];
    } catch (error) {
      console.error('HistoryManager: Error getting history:', error);
      return [];
    }
  }

  // Get filtered history
  async getFilteredHistory(filter = 'recent', searchQuery = '') {
    try {
      let history = await this.getHistory();

      // Apply search filter
      if (searchQuery.trim()) {
        const query = searchQuery.toLowerCase().trim();
        history = history.filter(item =>
          item.title.toLowerCase().includes(query) ||
          item.artist.toLowerCase().includes(query)
        );
      }

      // Apply sorting filter
      switch (filter) {
        case 'most_played':
          history.sort((a, b) => b.playCount - a.playCount);
          break;
        case 'most_time':
          history.sort((a, b) => b.listenDuration - a.listenDuration);
          break;
        case 'recent':
        default:
          history.sort((a, b) => b.lastPlayed - a.lastPlayed);
          break;
      }

      return history;
    } catch (error) {
      console.error('HistoryManager: Error getting filtered history:', error);
      return [];
    }
  }

  // Update weekly stats
  async updateWeeklyStats(listenDuration) {
    try {
      let stats = await this.getWeeklyStats();
      const now = new Date();
      const dayOfWeek = now.getDay(); // 0 = Sunday, 6 = Saturday

      // Check if we need to reset for new week
      const weekStart = new Date(stats.weekStart);
      const currentWeekStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay());
      currentWeekStart.setHours(0, 0, 0, 0);

      if (weekStart.getTime() !== currentWeekStart.getTime()) {
        // New week, reset stats
        stats = createWeeklyStats();
        stats.weekStart = currentWeekStart.getTime();
      }

      // Update stats - only increment songs played for new plays
      stats.totalListenTime += listenDuration;
      stats.songsPlayed += 1;
      stats.dailyStats[dayOfWeek] += listenDuration;

      await AsyncStorage.setItem(WEEKLY_STATS_KEY, JSON.stringify(stats));
      console.log('HistoryManager: Weekly stats updated', stats);

    } catch (error) {
      console.error('HistoryManager: Error updating weekly stats:', error);
    }
  }

  // Get weekly stats
  async getWeeklyStats() {
    try {
      const statsData = await AsyncStorage.getItem(WEEKLY_STATS_KEY);
      return statsData ? JSON.parse(statsData) : createWeeklyStats();
    } catch (error) {
      console.error('HistoryManager: Error getting weekly stats:', error);
      return createWeeklyStats();
    }
  }

  // Ensure weekly stats exist
  async ensureWeeklyStatsExist() {
    try {
      const existing = await AsyncStorage.getItem(WEEKLY_STATS_KEY);
      if (!existing) {
        const newStats = createWeeklyStats();
        await AsyncStorage.setItem(WEEKLY_STATS_KEY, JSON.stringify(newStats));
      }
    } catch (error) {
      console.error('HistoryManager: Error ensuring weekly stats exist:', error);
    }
  }

  // Clear all history
  async clearHistory() {
    try {
      await AsyncStorage.removeItem(HISTORY_STORAGE_KEY);
      await AsyncStorage.removeItem(WEEKLY_STATS_KEY);
      console.log('HistoryManager: History cleared');
      return true;
    } catch (error) {
      console.error('HistoryManager: Error clearing history:', error);
      return false;
    }
  }

  // Reset play counts for testing
  async resetPlayCounts() {
    try {
      const history = await this.getHistory();
      const resetHistory = history.map(item => ({
        ...item,
        playCount: 1
      }));
      await AsyncStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify(resetHistory));
      console.log('HistoryManager: Play counts reset');
      return true;
    } catch (error) {
      console.error('HistoryManager: Error resetting play counts:', error);
      return false;
    }
  }

  // Get history stats
  async getHistoryStats() {
    try {
      const history = await this.getHistory();
      const weeklyStats = await this.getWeeklyStats();

      const totalSongs = history.length;
      const totalPlayCount = history.reduce((sum, item) => sum + item.playCount, 0);
      const totalListenTime = history.reduce((sum, item) => sum + item.listenDuration, 0);

      return {
        totalSongs,
        totalPlayCount,
        totalListenTime,
        weeklyStats,
        averageListenTime: totalSongs > 0 ? totalListenTime / totalSongs : 0,
      };
    } catch (error) {
      console.error('HistoryManager: Error getting history stats:', error);
      return {
        totalSongs: 0,
        totalPlayCount: 0,
        totalListenTime: 0,
        weeklyStats: createWeeklyStats(),
        averageListenTime: 0,
      };
    }
  }

  // Format duration for display
  formatDuration(milliseconds) {
    if (!milliseconds || milliseconds < 0) return '0:00';

    const totalSeconds = Math.floor(milliseconds / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
  }

  // Cleanup old entries (keep only last 30 days)
  async cleanupOldEntries() {
    try {
      const history = await this.getHistory();
      const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);

      const filteredHistory = history.filter(item => item.lastPlayed > thirtyDaysAgo);

      if (filteredHistory.length !== history.length) {
        await AsyncStorage.setItem(HISTORY_STORAGE_KEY, JSON.stringify(filteredHistory));
        console.log(`HistoryManager: Cleaned up ${history.length - filteredHistory.length} old entries`);
      }
    } catch (error) {
      console.error('HistoryManager: Error cleaning up old entries:', error);
    }
  }

  // Cleanup on app close/background
  cleanup() {
    try {
      // Save current progress before cleanup
      if (this.isTracking && this.currentTrack && this.startTime) {
        const currentDuration = Date.now() - this.startTime;
        // Use synchronous storage for immediate cleanup
        this.addToHistory(this.currentTrack, currentDuration, false).catch(error => {
          console.error('HistoryManager: Error saving on cleanup:', error);
        });
      }

      // Only clear tracking on final cleanup, not on background
      if (this.trackingInterval) {
        clearInterval(this.trackingInterval);
        this.trackingInterval = null;
      }

      this.isTracking = false;
      this.currentTrack = null;
      this.startTime = null;
      this.lastSavedDuration = 0;

      console.log('HistoryManager: Cleanup completed');
    } catch (error) {
      console.error('HistoryManager: Error during cleanup:', error);
    }
  }

  // Save progress without stopping tracking (for background mode)
  async saveProgressBackground() {
    try {
      if (this.isTracking && this.currentTrack && this.startTime) {
        const currentDuration = Date.now() - this.startTime;
        await this.addToHistory(this.currentTrack, currentDuration, false);
        this.lastSavedDuration = currentDuration;
        console.log(`HistoryManager: Background progress saved - ${currentDuration}ms`);
      }
    } catch (error) {
      console.error('HistoryManager: Error saving background progress:', error);
    }
  }

  // Get tracking state
  get isCurrentlyTracking() {
    return this.isTracking;
  }

  // Get current tracking info
  getCurrentTrackingInfo() {
    return {
      isTracking: this.isTracking,
      currentTrack: this.currentTrack,
      startTime: this.startTime,
      duration: this.startTime ? Date.now() - this.startTime : 0
    };
  }

  // Search history
  async searchHistory(query) {
    try {
      const history = await this.getHistory();
      const searchTerm = query.toLowerCase().trim();

      if (!searchTerm) {
        return [];
      }

      return history.filter(item =>
        item &&
        item.id &&
        (item.title?.toLowerCase().includes(searchTerm) ||
         item.artist?.toLowerCase().includes(searchTerm) ||
         item.album?.toLowerCase().includes(searchTerm))
      );
    } catch (error) {
      console.error('HistoryManager: Error searching history:', error);
      return [];
    }
  }
}

// Create singleton instance
const historyManager = new HistoryManager();

export default historyManager;
