import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import { useTheme } from '@react-navigation/native';

export const HistoryFilters = ({ activeFilter, onFilterChange }) => {
  const { colors, dark } = useTheme();
  const styles = getThemedStyles(colors, dark);

  const filters = [
    { key: 'recent', label: 'Recent' },
    { key: 'mostPlayed', label: 'Most Played' },
    { key: 'mostTime', label: 'Most Time' },
  ];

  return (
    <View style={styles.container}>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {filters.map((filter) => (
          <TouchableOpacity
            key={filter.key}
            style={[
              styles.filterButton,
              activeFilter === filter.key && styles.activeFilterButton,
              { 
                backgroundColor: activeFilter === filter.key 
                  ? colors.primary 
                  : (dark ? colors.cardSurface : colors.card),
                borderColor: colors.border,
              }
            ]}
            onPress={() => onFilterChange(filter.key)}
          >
            <Text
              style={[
                styles.filterText,
                activeFilter === filter.key && styles.activeFilterText,
                { 
                  color: activeFilter === filter.key 
                    ? '#FFFFFF' 
                    : colors.text 
                }
              ]}
            >
              {filter.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const getThemedStyles = (colors, dark) => StyleSheet.create({
  container: {
    paddingVertical: 16,
    paddingHorizontal: 8,
  },
  scrollContent: {
    paddingHorizontal: 16,
    gap: 12,
    justifyContent: 'center',
    alignItems: 'center',
    flexGrow: 1,
  },
  filterButton: {
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    borderWidth: 1,
    minWidth: 100,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 4,
  },
  activeFilterButton: {
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3,
  },
  filterText: {
    fontSize: 15,
    fontWeight: '500',
    textAlign: 'center',
  },
  activeFilterText: {
    fontWeight: '700',
  },
});
